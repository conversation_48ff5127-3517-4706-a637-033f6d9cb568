import json
import pytest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the data directory to the path to import the lambda function
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'data'))

from lambda_function import handler, parse_api_gateway_event, process_file_request


class TestAPIGatewayLambda:
    
    def test_parse_api_gateway_event_query_params(self):
        """Test parsing filename from query parameters"""
        event = {
            'queryStringParameters': {
                'filename': 'test_file.7z',
                'bucket': 'test-bucket'
            }
        }
        
        filename, bucket = parse_api_gateway_event(event)
        assert filename == 'test_file.7z'
        assert bucket == 'test-bucket'
    
    def test_parse_api_gateway_event_body(self):
        """Test parsing filename from request body"""
        event = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': json.dumps({
                'filename': 'body_file.7z',
                'bucket': 'body-bucket'
            })
        }
        
        filename, bucket = parse_api_gateway_event(event)
        assert filename == 'body_file.7z'
        assert bucket == 'body-bucket'
    
    def test_parse_api_gateway_event_no_filename(self):
        """Test parsing when no filename is provided"""
        event = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': None
        }
        
        filename, bucket = parse_api_gateway_event(event)
        assert filename is None
        assert bucket is None
    
    @patch('lambda_function.S3FileSystemUnitOfWork')
    @patch('lambda_function.genai_config')
    def test_process_file_request_file_not_found(self, mock_config, mock_s3_class):
        """Test processing when file doesn't exist in S3"""
        mock_config.S3_BUCKET = 'test-bucket'
        mock_s3 = Mock()
        mock_s3.get_file.side_effect = Exception("File not found")
        mock_s3_class.return_value = mock_s3
        
        status, message, error = process_file_request('nonexistent.7z')
        
        assert status == 404
        assert error is not None
        assert 'not found' in error
    
    @patch('lambda_function.S3FileSystemUnitOfWork')
    @patch('lambda_function.genai_config')
    def test_process_file_request_invalid_extension(self, mock_config, mock_s3_class):
        """Test processing file without .7z extension"""
        mock_config.S3_BUCKET = 'test-bucket'
        mock_s3 = Mock()
        mock_s3.get_file.return_value = b'file content'
        mock_s3_class.return_value = mock_s3
        
        status, message, error = process_file_request('test.txt')
        
        # Should add .7z extension automatically
        mock_s3.get_file.assert_called_with('test.txt.7z')
    
    @patch('lambda_function.process_file_request')
    def test_handler_success(self, mock_process):
        """Test successful handler execution"""
        mock_process.return_value = (200, "Success", None)
        
        event = {
            'queryStringParameters': {
                'filename': 'test.7z'
            }
        }
        
        response = handler(event, None)
        
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['message'] == 'Success'
    
    @patch('lambda_function.process_file_request')
    def test_handler_missing_filename(self, mock_process):
        """Test handler with missing filename"""
        event = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': None
        }
        
        response = handler(event, None)
        
        assert response['statusCode'] == 400
        body = json.loads(response['body'])
        assert 'filename' in body['error']
    
    @patch('lambda_function.process_file_request')
    def test_handler_internal_error(self, mock_process):
        """Test handler with internal error"""
        mock_process.side_effect = Exception("Internal error")
        
        event = {
            'queryStringParameters': {
                'filename': 'test.7z'
            }
        }
        
        response = handler(event, None)
        
        assert response['statusCode'] == 500
        body = json.loads(response['body'])
        assert 'Internal server error' in body['error']


if __name__ == '__main__':
    pytest.main([__file__])
