import logging
from typing import <PERSON><PERSON>, List, Dict
from src.domain.models import TasacionRequest
from src.domain.services import TasacionValidator, FileUnzipper
from src.infrastructure.s3_repository import S3Repository
from src.infrastructure.sqs_repository import SQSRepository
from src.infrastructure.core.config import genai_config

logger = logging.getLogger(__name__)

class TasacionProcessor:
    def __init__(self):
        self.s3_bucket = genai_config.S3_BUCKET
        self.s3_repo = S3Repository()
        self.queue_url = getattr(genai_config, 'SQS_URL', None)
        self.sqs_repo = SQSRepository(queue_url=self.queue_url)

    def process(self, payload: Dict) -> Tuple[int, Dict]:
        req = TasacionRequest(payload['id_tasacion'], payload['files'])
        all_ok, found = TasacionValidator.validate_all_exist(
            req, self.s3_bucket, self.s3_repo
        )
        if not all_ok:
            missing = [f for f in req.files if f not in found]
            return 400, {"error": f"Faltan archivos: {missing}"}
        for key in found:
            if key.lower().endswith('.zip'):
                FileUnzipper.unzip_and_upload(key, self.s3_bucket, self.s3_repo)
        self.sqs_repo.send_message({"id_tasacion": req.id_tasacion, "files": found})
        return 200, {"message": "Operación procesada exitosamente."}
