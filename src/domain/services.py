import io
import zipfile
from typing import <PERSON><PERSON>, List
from src.domain.models import TasacionRequest

class TasacionValidator:
    @staticmethod
    def validate_all_exist(
        request: TasacionRequest, bucket: str, s3_repo
    ) -> Tuple[bool, List[str]]:
        found = [key for key in request.files if s3_repo.exists(bucket, key)]
        return len(found) == len(request.files), found

class FileUnzipper:
    @staticmethod
    def unzip_and_upload(key: str, bucket: str, s3_repo) -> None:
        raw = s3_repo.get_object(bucket, key)
        with zipfile.ZipFile(io.BytesIO(raw)) as archive:
            for member in archive.namelist():
                content = archive.read(member)
                target = f"unzipped/{key.replace('.zip', '')}/{member}"
                s3_repo.put_object(bucket, target, content)