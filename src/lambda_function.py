import json
import logging

from genai.libs.observability import setup_observability
from genai.libs.observability.genai_lambda import trace_lambda
#from src.aws.s3_manager import S3Manager

from src.infrastructure.core.config import genai_config
from src.application.processor import TasacionProcessor
# please do not remove, this preconfigures opentelemetry!!


default_dimensions = {
    "Lambda": genai_config.GENAI_NAME,
    "Application": genai_config.GENAI_APPLICATION,
    "Product": genai_config.GENAI_PRODUCT,
    "service.name": genai_config.GENAI_NAME,
}

setup_observability(
    environment=genai_config.GENAI_ENVIRONMENT,
    default_dimensions=default_dimensions,
)


logger = logging.getLogger(__name__)

processor = TasacionProcessor()

# Read and Process event
def process_event(event):

    event = json.loads(event["body"]) if "body" in event else event
    return event


def http_ok_mssg(mssg: str = None) -> dict:
    """
    Creates an HTTP Json message with status 200 and a mssg

    Parameters:
    -mssg (str)=None: message that wants to be included in the HTTP mssg body.
    If it's None it will put a default message.

    Returns:
    - dict: the HTTP message.
    """
    if mssg is None:
        mssg = "Orchestrator is working correctly."

    response = {
        'statusCode': 200,
        'body': json.dumps(mssg)
    }

def http_response(status: int, body: dict) -> dict:
    return {'statusCode': status, 'body': json.dumps(body)}

@trace_lambda
def handler(event, context):
    logger.info("Inicio de procesamiento de evento")
    payload = process_event(event)
    status, resp = processor.process(payload)
    return http_response(status, resp)

"""
@trace_lambda
def handler(event, context):
    logger.info("Processing event...")
    s3_path = genai_config.S3_BUCKET + "/path/to/s3"
    # Procesar evento
    event = process_event(event)
    logger.info("Event processed successfully.")

    # Listar s3
    s3_manager = S3Manager(logger)

    # Unzip
    # Write back to S3 with unzipped files.

    # Send to SQS N paths messages with N files to be preocessed.

    return http_ok_mssg()
"""

