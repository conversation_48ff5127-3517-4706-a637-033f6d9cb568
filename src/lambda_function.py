import json
import logging

from genai.libs.observability import setup_observability
from genai.libs.observability.genai_lambda import trace_lambda
#from src.aws.s3_manager import S3Manager

from src.infrastructure.core.config import genai_config
from src.application.processor import TasacionProcessor
# please do not remove, this preconfigures opentelemetry!!


default_dimensions = {
    "Lambda": genai_config.GENAI_NAME,
    "Application": genai_config.GENAI_APPLICATION,
    "Product": genai_config.GENAI_PRODUCT,
    "service.name": genai_config.GENAI_NAME,
}

setup_observability(
    environment=genai_config.GENAI_ENVIRONMENT,
    default_dimensions=default_dimensions,
)


logger = logging.getLogger(__name__)

processor = TasacionProcessor()

# Read and Process API Gateway event
def process_event(event):
    """
    Process API Gateway event to extract filename and bucket information.

    Args:
        event: API Gateway event

    Returns:
        dict: Processed event with filename and bucket
    """
    # Try to get filename from query parameters
    query_params = event.get('queryStringParameters') or {}
    filename = query_params.get('filename')

    # Try to get filename from path parameters
    if not filename:
        path_params = event.get('pathParameters') or {}
        filename = path_params.get('filename')

    # Try to get filename from request body
    if not filename and event.get('body'):
        try:
            body = json.loads(event['body'])
            filename = body.get('filename')
        except json.JSONDecodeError:
            pass

    # Get bucket name (optional)
    bucket_name = query_params.get('bucket') or None
    if not bucket_name and event.get('body'):
        try:
            body = json.loads(event['body'])
            bucket_name = body.get('bucket')
        except json.JSONDecodeError:
            pass

    return {
        'filename': filename,
        'bucket': bucket_name
    }


def http_ok_mssg(mssg: str = None) -> dict:
    """
    Creates an HTTP Json message with status 200 and a mssg

    Parameters:
    -mssg (str)=None: message that wants to be included in the HTTP mssg body.
    If it's None it will put a default message.

    Returns:
    - dict: the HTTP message.
    """
    if mssg is None:
        mssg = "Orchestrator is working correctly."

    response = {
        'statusCode': 200,
        'body': json.dumps(mssg)
    }
    return response

def http_response(status: int, body: dict) -> dict:
    return {'statusCode': status, 'body': json.dumps(body)}

#@trace_lambda
def handler(event, context):
    logger.info("Inicio de procesamiento de evento API Gateway")
    logger.info(f"Event: {json.dumps(event)}")

    try:
        # Process API Gateway event
        payload = process_event(event)
        filename = payload.get('filename')
        bucket = payload.get('bucket')

        # Validate required parameters
        if not filename:
            return http_response(400, {"error": "Missing required parameter: filename"})

        # Ensure filename has .7z extension for 7z files or appropriate extension
        if not filename.endswith(('.7z', '.zip')):
            # Default to .7z if no extension provided
            filename = filename + '.7z'

        # Use default bucket if not provided
        if not bucket:
            bucket = genai_config.S3_BUCKET

        # Create a modified payload for the processor
        # The processor expects id_tasacion and files list
        modified_payload = {
            'id_tasacion': f"api_request_{filename.replace('/', '_').replace('.', '_')}",
            'files': [filename],
            'bucket': bucket
        }

        # Process the file
        status, resp = processor.process(modified_payload)
        return http_response(status, resp)

    except Exception as e:
        logger.error(f"Error processing API Gateway event: {e}")
        return http_response(500, {"error": f"Internal server error: {str(e)}"})

"""
@trace_lambda
def handler(event, context):
    logger.info("Processing event...")
    s3_path = genai_config.S3_BUCKET + "/path/to/s3"
    # Procesar evento
    event = process_event(event)
    logger.info("Event processed successfully.")

    # Listar s3
    s3_manager = S3Manager(logger)

    # Unzip
    # Write back to S3 with unzipped files.

    # Send to SQS N paths messages with N files to be preocessed.

    return http_ok_mssg()
"""

