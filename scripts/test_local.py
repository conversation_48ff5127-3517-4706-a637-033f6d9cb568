#!/usr/bin/env python3
"""
Script para probar la funcionalidad de la Lambda localmente.
"""

import json
import sys
import os

# Add the data directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'data'))

from src.lambda_function import handler


def test_get_request():
    """Test GET request with query parameters"""
    print("Testing GET request...")
    
    event = {
        "resource": "/process-file",
        "path": "/process-file",
        "httpMethod": "GET",
        "headers": {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        "queryStringParameters": {
            "filename": "test_company/test_provider/test_file.7z"
        },
        "pathParameters": None,
        "body": None,
        "isBase64Encoded": False
    }
    
    try:
        response = handler(event, None)
        print(f"Status Code: {response['statusCode']}")
        print(f"Response Body: {response['body']}")
        print(f"Headers: {response.get('headers', {})}")
        return response
    except Exception as e:
        print(f"Error: {e}")
        return None


def test_post_request():
    """Test POST request with body"""
    print("\nTesting POST request...")
    
    event = {
        "resource": "/process-file",
        "path": "/process-file",
        "httpMethod": "POST",
        "headers": {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        "queryStringParameters": None,
        "pathParameters": None,
        "body": json.dumps({
            "filename": "company2/provider2/batch_data.7z",
            "bucket": "custom-bucket"
        }),
        "isBase64Encoded": False
    }
    
    try:
        response = handler(event, None)
        print(f"Status Code: {response['statusCode']}")
        print(f"Response Body: {response['body']}")
        print(f"Headers: {response.get('headers', {})}")
        return response
    except Exception as e:
        print(f"Error: {e}")
        return None


def test_missing_filename():
    """Test request without filename"""
    print("\nTesting request without filename...")
    
    event = {
        "resource": "/process-file",
        "path": "/process-file",
        "httpMethod": "GET",
        "headers": {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        "queryStringParameters": None,
        "pathParameters": None,
        "body": None,
        "isBase64Encoded": False
    }
    
    try:
        response = handler(event, None)
        print(f"Status Code: {response['statusCode']}")
        print(f"Response Body: {response['body']}")
        return response
    except Exception as e:
        print(f"Error: {e}")
        return None


def test_with_path_parameter():
    """Test request with path parameter"""
    print("\nTesting request with path parameter...")
    
    event = {
        "resource": "/process-file/{filename}",
        "path": "/process-file/test_file.7z",
        "httpMethod": "GET",
        "headers": {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        "queryStringParameters": None,
        "pathParameters": {
            "filename": "path_company/path_provider/path_file.7z"
        },
        "body": None,
        "isBase64Encoded": False
    }
    
    try:
        response = handler(event, None)
        print(f"Status Code: {response['statusCode']}")
        print(f"Response Body: {response['body']}")
        return response
    except Exception as e:
        print(f"Error: {e}")
        return None


def main():
    """Run all tests"""
    print("=" * 60)
    print("TESTING LAMBDA API GATEWAY FUNCTIONALITY")
    print("=" * 60)
    
    # Test different scenarios
    test_get_request()
    test_post_request()
    test_missing_filename()
    test_with_path_parameter()
    
    print("\n" + "=" * 60)
    print("TESTING COMPLETED")
    print("=" * 60)


if __name__ == "__main__":
    main()
