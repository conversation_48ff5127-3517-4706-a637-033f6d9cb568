import logging 
import urllib.parse 
import json 
import time 

from src.infrastructure.core.config import genai_config 
from src.infrastructure.genai.filesystem import S3FileSystemUnitOfWork 
from src.application.utils import (
    get_file_name_and_company,
    should_extract_s3,
    extract_7z_from_s3
)

env = genai_config.GENAI_ENVIRONMENT
trace_lambda_condition = (env != "local") and (env != "test")
# please do not remove, this preconfigures opentelemetry!!
if trace_lambda_condition: # pragma: no cover
    from genai.libs.observability import setup_observability
    from genai.libs.observability.genai_lambda import trace_lambda

    default_dimensions = { # pragma: no cover
    "Lambda": genai_config.GENAI_NAME,
    "Application": genai_config.GENAI_APPLICATION,
    "Product": genai_config.GENAI_PRODUCT,
    "service.name": genai_config.GENAI_NAME,
    }

    setup_observability(
        environment=genai_config.GENAI_ENVIRONMENT,
        default_dimensions=default_dimensions,
    )

logger = logging.getLogger(__name__) 

def conditional_trace_lambda(apply_decorator: bool):
    """
    Returns the trace_lambda function for the decorator if the conditional it's
    True.

    Parameters:
    - apply_decorator (bool): indicates if the decorator trace_lambda has to be
    applied. 
    """
    def decorator(func):
        if apply_decorator:
            return trace_lambda(
                environment=env,
                default_dimensions=default_dimensions
            )(func)
        else:
            return func
    return decorator

def http_ok_mssg(mssg=None):  
    if mssg is None:
        mssg = "Succesfully processed"

    response= {
                'statusCode': 200,
                'body': json.dumps(mssg)   
            }
    return response

def process_s3_event(mssg): 
    """gets the body of the bucket notification and process the event.

    Args:
        mssg (json): json to be treated.
    """
    logger.info("Starting processing of the s3 event...")
    try:
        s3_info = mssg["s3"]
        bucket_name = s3_info["bucket"]["name"]
        object_key = urllib.parse.unquote_plus(
            s3_info["object"]["key"], encoding="utf-8"
        )
    except KeyError as e:
        logger.error("Missing expected key in S3 event: %s", e)
        return

    logger.info("bucket_name_founded=%s", bucket_name)
    logger.info("file founded (object_key)=%s", object_key)

    # call the buckect calls
    call_s3 = S3FileSystemUnitOfWork(bucket_name)

    # variables del config:
    output_folder_prefix = genai_config.S3_OUTPUT_PATH
    output_folder_text_prefix = genai_config.S3_OUTPUT_PATH_TEXT
    output_folder_images_prefix = genai_config.S3_OUTPUT_PATH_IMAGES
    output_folder_docs_prefix = genai_config.S3_OUTPUT_PATH_DOCS
    output_folder_register_prefix = genai_config.S3_OUTPUT_PATH_REGISTER

    if object_key.endswith(".7z"):
        logger.info("the file is a .7z file, processing.")
        company, provider, folder_name = get_file_name_and_company(object_key)
        folder_name = folder_name.replace(".7z", "")
        zip_folder_processed = f"{output_folder_prefix}/{company}/{provider}"
        extracted_folder_prefix = f"{output_folder_prefix}/{company}"
        extracted_folder_text_prefix = f"{output_folder_text_prefix}/{company}"
        extracted_folder_images_prefix = f"{output_folder_images_prefix}/{company}"
        extracted_folder_docs_prefix = f"{output_folder_docs_prefix}/{company}"
        extracted_folder_register_prefix = f"{output_folder_register_prefix}/{company}"


        logger.info(f"Processing company: {folder_name}")

        if should_extract_s3(call_s3, object_key, extracted_folder_prefix,provider):
            extract_7z_from_s3(call_s3, object_key, extracted_folder_prefix, extracted_folder_text_prefix, extracted_folder_images_prefix, extracted_folder_docs_prefix, extracted_folder_register_prefix)
            call_s3.move_file(
            old_file=object_key, 
            new_file=zip_folder_processed
            )
        else:
            logger.info(
                f"It is not needed to extract '{object_key}'. Folder is already updated."
            )

        # TO DO time out decorator
        
    else:
        print("File founded is not a .7z")


@conditional_trace_lambda(apply_decorator=trace_lambda_condition)
def handler(event, context): 
    t1 = time.perf_counter()
    logger.info("Processing event...")
    mssg = None

    # este código para local
    # local_event_path = "/home/<USER>/certificate_extraction/genai_extcerts_lambda1/data/event_example.json"
    # with open(local_event_path, "r") as file:
    #     local_event = json.load(file)
    # if local_event["Records"]: 
    #     for record in local_event["Records"]:
    #         body = json.loads(record["body"])
    #         if "Records" in body:
    #             for message in body["Records"]:
    #                 process_s3_event(message)
    
    if event.get("Records"):
        for record in event["Records"]:
            body = json.loads(record["body"])
            if "Records" in body:
                for message in body["Records"]:
                    process_s3_event(message)
    else:
        mssg = "Skipped processing for bad event"

    t2 = time.perf_counter()
    logger.info(f"\n\nLambda Code Took:{(t2 - t1)/60} minutes")
    return http_ok_mssg(mssg)

if __name__=="__main__":
    handler(event="test", context="")
