# Lambda de Procesamiento de Archivos 7z - API Gateway

## Descripción

Esta Lambda ha sido modificada para ser activada por eventos de API Gateway en lugar de eventos de S3. La nueva funcionalidad permite:

1. <PERSON><PERSON><PERSON> peticiones HTTP con el nombre de un archivo comprimido (.7z)
2. Verificar que el archivo existe en S3
3. Descomprimir el archivo y organizar los contenidos en diferentes carpetas según el tipo de archivo
4. Mover el archivo original a una carpeta de procesados

## Configuración

### Variables de Entorno

Las siguientes configuraciones están disponibles en `src/infrastructure/core/config.py`:

- `S3_BUCKET`: Bucket de S3 donde se encuentran los archivos
- `S3_OUTPUT_PATH`: Ruta base para archivos procesados
- `S3_OUTPUT_PATH_TEXT`: Ruta para archivos de texto (.txt, .csv, .json, .xml)
- `S3_OUTPUT_PATH_IMAGES`: Ruta para imágenes (.jpg, .png, .gif, etc.)
- `S3_OUTPUT_PATH_DOCS`: Ruta para documentos (.pdf, .doc, .xls, etc.)
- `S3_OUTPUT_PATH_REGISTER`: Ruta para archivos de registro (.log, .reg)

## API

### Endpoint

`GET /process-file?filename=<nombre_archivo>`
`POST /process-file`

### Parámetros

#### GET Request
- `filename` (query parameter): Nombre del archivo .7z a procesar
- `bucket` (query parameter, opcional): Nombre del bucket S3 (usa el por defecto si no se especifica)

#### POST Request
```json
{
  "filename": "company/provider/archivo.7z",
  "bucket": "bucket-name" // opcional
}
```

### Respuestas

#### Éxito (200)
```json
{
  "message": "Successfully processed and extracted archivo.7z"
}
```

#### Error de archivo no encontrado (404)
```json
{
  "error": "File archivo.7z not found in S3 bucket bucket-name"
}
```

#### Error de parámetros (400)
```json
{
  "error": "Missing required parameter: filename"
}
```

#### Error interno (500)
```json
{
  "error": "Internal server error: descripción del error"
}
```

## Estructura de Archivos

### Entrada
Los archivos deben estar en S3 con la estructura:
```
bucket/
├── company1/
│   └── provider1/
│       └── archivo.7z
└── company2/
    └── provider2/
        └── otro_archivo.7z
```

### Salida
Después del procesamiento:
```
bucket/
├── processed/
│   ├── company1/
│   │   └── provider1/
│   │       └── archivo.7z  # archivo movido
│   └── text/
│       └── company1/
│           ├── documento.txt
│           └── datos.csv
├── processed/images/
│   └── company1/
│       └── imagen.jpg
└── processed/docs/
    └── company1/
        └── reporte.pdf
```

## Funciones Principales

### `handler(event, context)`
Función principal que maneja eventos de API Gateway.

### `parse_api_gateway_event(event)`
Extrae el nombre del archivo y bucket del evento de API Gateway.

### `process_file_request(filename, bucket_name)`
Procesa la solicitud de extracción de archivo:
1. Verifica que el archivo existe en S3
2. Extrae el archivo .7z
3. Organiza los archivos por tipo
4. Mueve el archivo original

### `extract_7z_from_s3(...)`
Extrae archivos .7z desde S3 y los sube organizados por tipo.

### `should_extract_s3(...)`
Determina si un archivo debe ser extraído basándose en fechas de modificación.

### `get_file_name_and_company(object_key)`
Extrae información de empresa y proveedor del path del archivo.

## Ejemplos de Uso

### cURL GET
```bash
curl -X GET "https://api.example.com/process-file?filename=company1/provider1/datos.7z"
```

### cURL POST
```bash
curl -X POST "https://api.example.com/process-file" \
  -H "Content-Type: application/json" \
  -d '{"filename": "company1/provider1/datos.7z"}'
```

### Python
```python
import requests

# GET request
response = requests.get(
    "https://api.example.com/process-file",
    params={"filename": "company1/provider1/datos.7z"}
)

# POST request
response = requests.post(
    "https://api.example.com/process-file",
    json={"filename": "company1/provider1/datos.7z"}
)
```

## Pruebas

### Archivos de Ejemplo
- `data/api_gateway_event_example.json`: Evento GET de ejemplo
- `data/api_gateway_post_event_example.json`: Evento POST de ejemplo

### Ejecución Local
```python
python data/lambda_function.py
```

## Dependencias

- `py7zr`: Para extraer archivos .7z
- `boto3`: Cliente AWS S3
- `pydantic`: Configuración
- `genai.libs.observability`: Observabilidad (opcional)

## Notas

- Los archivos deben tener extensión .7z
- Si no se especifica la extensión, se añade automáticamente
- El sistema verifica fechas de modificación para evitar reprocesamiento
- Los archivos se organizan automáticamente por tipo de extensión
